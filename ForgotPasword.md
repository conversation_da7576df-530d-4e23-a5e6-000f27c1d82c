# Forgot Password Feature - Task List

## Tổng quan dự án
Dựa vào phân tích cấu trúc dự án MERN stack (Taskflow), tôi đã xác định được:
- **Backend**: Express.js với MongoDB, đã có system auth cơ bản (register, login, verify)
- **Frontend**: React với Material-UI, đã có LoginForm và RegisterForm
- **Email Provider**: Đã có NodemailerProvider setup sẵn với Gmail SMTP
- **Database**: User model đã có các field cần thiết (email, password, verifyToken)

---

## BACKEND TASKS

### 1. Database & Model Updates
- [x] **Thêm field `resetPasswordToken` vào User Model**
  - Cập nhật `userModel.js` để thêm field `resetPasswordToken` với type String
  - Cập nhật `resetPasswordExpires` với type Date để set thời gian hết hạn token
  - Cập nhật schema validation trong USER_COLLECTION_SCHEMA

- [x] **Thêm method tìm user bằng reset token**
  - Thêm function `findOneByResetToken` vào userModel.js
  - Function này sẽ tìm user có `resetPasswordToken` hợp lệ và chưa hết hạn

### 2. Service Layer Updates  
- [x] **Tạo forgot password service**
  - Trong `userService.js`, tạo function `forgotPassword(email)`
  - Tạo reset token ngẫu nhiên (crypto.randomBytes)
  - Set thời gian hết hạn token (1 giờ)
  - Gửi email chứa link reset password
  
- [x] **Tạo reset password service**
  - Tạo function `resetPassword(token, newPassword)`
  - Validate token và kiểm tra thời gian hết hạn
  - Hash password mới và cập nhật vào database
  - Clear reset token sau khi reset thành công

### 3. Controller Updates
- [x] **Thêm forgotPassword controller**
  - Trong `userController.js`, tạo function `forgotPassword`
  - Nhận email từ request body
  - Gọi service và trả về response phù hợp
  
- [x] **Thêm resetPassword controller**
  - Tạo function `resetPassword` 
  - Nhận token và newPassword từ request
  - Validate và gọi service tương ứng

### 4. Route Updates
- [x] **Thêm forgot password routes**
  - Trong `userRoute.js`, thêm route `POST /forgot-password`
  - Thêm route `PUT /reset-password/:token`
  - Áp dụng validation middleware phù hợp

### 5. Validation Updates
- [x] **Tạo validation cho forgot password**
  - Trong `userValidation.js`, tạo `forgotPassword` validation
  - Validate email format
- [x] **Tạo `resetPassword` validation cho token và password mới**

### 6. Email Template Updates
- [x] **Tạo forgot password email template**
  - Trong `emailTemplates.js`, tạo template `forgotPasswordEmail`
  - Template chứa link reset password với token
  - Design responsive và user-friendly
  
- [x] **Tạo password reset success email template**
  - Template thông báo password đã được reset thành công
  - Bao gồm thông tin security và hướng dẫn

### 7. Provider Updates  
- [x] **Cập nhật NodemailerProvider nếu cần**
  - Kiểm tra và fix lỗi trong `sendMailProvider.js` (từ field sai)
  - Đảm bảo gửi email forgot password hoạt động ổn định

---

## FRONTEND TASKS

### 8. Pages & Components
- [x] **Tạo ForgotPasswordForm component**
  - Tạo file `web/src/pages/Auth/ForgotPasswordForm.jsx`
  - Form nhập email với validation
  - UI tương đồng với LoginForm (Material-UI)
  - Xử lý submit và hiển thị success message
  
- [x] **Tạo ResetPasswordForm component**
  - Tạo file `web/src/pages/Auth/ResetPasswordForm.jsx`  
  - Form nhập password mới và confirm password
  - Lấy token từ URL params
  - Validation mật khẩu mạnh

### 9. Redux State Management
- [x] **Thêm forgot password actions**
  - Trong `userSlice.js`, tạo `forgotPasswordAPI` thunk
  - Tạo `resetPasswordAPI` thunk  
  - Cập nhật state để track loading/error/success

### 10. API Integration
- [x] **Tạo API calls**
  - Trong `web/src/apis/`, tạo functions gọi forgot/reset password APIs
  - Xử lý response và error appropriately

### 11. Routing Updates
- [x] **Cập nhật routing**
  - Thêm route `/forgot-password` vào router
  - Thêm route `/reset-password/:token` 
  - Cập nhật navigation links

### 12. UI/UX Enhancements
- [x] **Thêm link "Forgot Password?" vào LoginForm**
  - Cập nhật `LoginForm.jsx` để thêm link đến forgot password
  - Positioning phù hợp trong UI
  
- [x] **Tạo success/error notifications**
  - Sử dụng toast notifications cho feedback
  - Hiển thị loading states appropriately

---

## TESTING & SECURITY TASKS

### 13. Security Implementation
- [ ] **Rate limiting cho forgot password**
  - Implement rate limiting để tránh spam requests
  - Giới hạn số lần request forgot password per email per hour
  
- [ ] **Token security**
  - Đảm bảo reset token được generate securely (crypto.randomBytes)
  - Set thời gian hết hạn hợp lý (1 giờ)
  - Token chỉ sử dụng được 1 lần

### 14. Error Handling
- [ ] **Xử lý edge cases**
  - Email không tồn tại trong hệ thống
  - Token đã hết hạn hoặc không hợp lệ
  - Network errors và server errors
  
- [ ] **User feedback messages**
  - Messages rõ ràng và hữu ích cho user
  - Không tiết lộ thông tin sensitive

### 15. Testing
- [ ] **Test forgot password flow**
  - Test với email hợp lệ và không hợp lệ
  - Test token expiration  
  - Test password reset thành công
  
- [ ] **Test UI components**
  - Test form validation
  - Test responsive design
  - Test error states và loading states

---

## DEPLOYMENT & DOCUMENTATION

### 16. Environment Configuration
- [ ] **Cập nhật environment variables**
  - Đảm bảo EMAIL_GMAIL và EMAIL_PASSWORD được config đúng
  - Thêm FRONTEND_URL cho link trong email
  - Cập nhật CORS settings nếu cần

### 17. Documentation
- [ ] **Cập nhật API documentation**
  - Document forgot password endpoints
  - Thêm examples và error codes
  
- [ ] **User guide**
  - Hướng dẫn sử dụng tính năng forgot password
  - Troubleshooting common issues

---

## PRIORITY ORDER
1. **High Priority**: Tasks 1-7 (Backend implementation)
2. **Medium Priority**: Tasks 8-12 (Frontend implementation)  
3. **Low Priority**: Tasks 13-17 (Testing, Security, Documentation)

---

## NOTES
- Sử dụng crypto module của Node.js để generate secure tokens
- Reset password link nên có format: `${FRONTEND_URL}/reset-password/${token}`
- Implement proper error handling và user feedback ở mọi step
- Đảm bảo email templates responsive và professional
